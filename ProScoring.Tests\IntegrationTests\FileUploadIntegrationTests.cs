using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Data.Sqlite;
using Microsoft.EntityFrameworkCore;
using Projects;
using ProScoring.Infrastructure.Database;
using ProScoring.Infrastructure.Services;
using ProScoring.Tests.Helpers;
using Xunit.Abstractions;

namespace ProScoring.Tests.IntegrationTests;

/// <summary>
/// Integration tests for file upload functionality.
/// These tests verify the file upload endpoints and database interactions
/// through end-to-end testing using a real HTTP client.
/// </summary>
[Collection(nameof(StaticIdGenerationUtilServiceForTesting))]
public sealed class FileUploadIntegrationTests
    : IClassFixture<WebApplicationFactory<ProScoring.Blazor.Program>>,
        IDisposable
{
    #region Fields
    private readonly ITestOutputHelper _output;
    private readonly WebApplicationFactory<ProScoring.Blazor.Program> _factory;
    private const int DefaultTimeoutSeconds = 30;
    private const int ExtendedTimeoutSeconds = 90;
    #endregion

    #region Constructor
    /// <summary>
    /// Initializes a new instance of the <see cref="FileUploadIntegrationTests"/> class.
    /// Sets up the test context including the web application factory and output helper.
    /// </summary>
    /// <param name="factory">The web application factory used to create test server instances.</param>
    /// <param name="output">The test output helper for logging test information.</param>
    /// <exception cref="ArgumentNullException">Thrown when factory or output is null.</exception>
    public FileUploadIntegrationTests(
        WebApplicationFactory<ProScoring.Blazor.Program> factory,
        ITestOutputHelper output
    )
    {
        _factory = factory ?? throw new ArgumentNullException(nameof(factory));
        _output = output ?? throw new ArgumentNullException(nameof(output));
    }
    #endregion

    #region Helper Methods
    /// <summary>
    /// Creates and configures a distributed application builder for testing.
    /// </summary>
    /// <returns>A task containing the configured builder.</returns>
    private static async Task<IDistributedApplicationTestingBuilder> CreateAppBuilderAsync()
    {
        var appBuilder = await DistributedApplicationTestingBuilder.CreateAsync<ProScoring_AppHost>();
        appBuilder.Services.ConfigureHttpClientDefaults(clientBuilder =>
        {
            clientBuilder.ConfigurePrimaryHttpMessageHandler(
                () =>
                    new HttpClientHandler
                    {
                        ServerCertificateCustomValidationCallback =
                            HttpClientHandler.DangerousAcceptAnyServerCertificateValidator, // ONLY FOR TESTING
                    }
            );
            clientBuilder.AddStandardResilienceHandler();
        });
        return appBuilder;
    }

    /// <summary>
    /// Waits for a resource to be in the running state.
    /// </summary>
    /// <param name="resourceNotificationService">The resource notification service.</param>
    /// <param name="resourceName">The name of the resource to wait for.</param>
    /// <param name="timeoutSeconds">The timeout in seconds.</param>
    /// <returns>A task representing the wait operation.</returns>
    private static async Task WaitForResourceAsync(
        ResourceNotificationService resourceNotificationService,
        string resourceName,
        int timeoutSeconds
    )
    {
        await resourceNotificationService
            .WaitForResourceAsync(resourceName, KnownResourceStates.Running)
            .WaitAsync(TimeSpan.FromSeconds(timeoutSeconds));
    }
    #endregion

    #region Test Methods
    /// <summary>
    /// Tests that the GoodbyeWorld endpoint returns the expected response.
    /// Verifies the basic functionality of the API endpoint through the full middleware pipeline.
    /// </summary>
    /// <returns>A task representing the asynchronous test operation.</returns>
    [Fact]
    public async Task GoodbyeWorld_ReturnsGoodbyeWorld()
    {
        // Arrange
        var appBuilder = await DistributedApplicationTestingBuilder.CreateAsync<ProScoring_AppHost>();
        appBuilder.Services.ConfigureHttpClientDefaults(clientBuilder =>
        {
            clientBuilder.ConfigurePrimaryHttpMessageHandler(
                () =>
                    new HttpClientHandler
                    {
                        ServerCertificateCustomValidationCallback =
                            HttpClientHandler.DangerousAcceptAnyServerCertificateValidator, // ONLY FOR TESTING
                    }
            );
            clientBuilder.AddStandardResilienceHandler();
        });

        await using var app = await appBuilder.BuildAsync();
        var resourceNotificationService = app.Services.GetRequiredService<ResourceNotificationService>();
        await app.StartAsync();
        _output.WriteLine("Application started.");

        var client = app.CreateHttpClient("proscoring-blazor");

        // Act
        await WaitForResourceAsync(resourceNotificationService, "proscoring-blazor", DefaultTimeoutSeconds);
        var response = await client.PostAsync("/api/file/goodbyeworld", null);

        // Assert
        response.EnsureSuccessStatusCode();
        var responseString = await response.Content.ReadAsStringAsync();
        Assert.Equal("Goodbye World", responseString);
    }

    /// <summary>
    /// Tests that the HelloWorld endpoint returns the expected response.
    /// Verifies the basic functionality of the API endpoint through the full middleware pipeline.
    /// </summary>
    /// <returns>A task representing the asynchronous test operation.</returns>
    [Fact]
    public async Task HelloWorld_ReturnsHelloWorld()
    {
        // Arrange

        // var client = _factory.CreateClient();
        var appBuilder = await DistributedApplicationTestingBuilder.CreateAsync<ProScoring_AppHost>();
        appBuilder.Services.ConfigureHttpClientDefaults(clientBuilder =>
        {
            clientBuilder.ConfigurePrimaryHttpMessageHandler(
                () =>
                    new HttpClientHandler
                    {
                        ServerCertificateCustomValidationCallback =
                            HttpClientHandler.DangerousAcceptAnyServerCertificateValidator, // ONLY FOR TESTING
                    }
            );
            clientBuilder.AddStandardResilienceHandler();
        });
        // To output logs to the xUnit.net ITestOutputHelper, consider adding a package from https://www.nuget.org/packages?q=xunit+logging

        await using var app = await appBuilder.BuildAsync();
        var resourceNotificationService = app.Services.GetRequiredService<ResourceNotificationService>();
        await app.StartAsync();
        _output.WriteLine("Application started.");

        var client = app.CreateHttpClient("proscoring-blazor");

        // Act
        await WaitForResourceAsync(resourceNotificationService, "proscoring-blazor", DefaultTimeoutSeconds);
        var response = await client.GetAsync("/api/file/helloworld");

        // Assert
        response.EnsureSuccessStatusCode();
        var responseString = await response.Content.ReadAsStringAsync();
        Assert.Equal("Hello World", responseString);
    }

    /// <summary>
    /// Tests that file upload saves to the database correctly.
    /// </summary>
    /// <remarks>
    /// This test is currently skipped as it needs to be rewritten after changes to the WebAPI.
    /// </remarks>
    [Fact(Skip = "Test needs to be rewritten after all other changes to the webapi, etc.")]
    public async Task UploadFile_SavesToDatabase()
    {
        // TODO: Rewrite this test to work with the current WebAPI implementation
        var appBuilder = await CreateAppBuilderAsync();

        // Configure database context
        appBuilder.Services.AddDbContext<ApplicationDbContext>(options =>
        {
            options.UseSqlite(new SqliteConnection("Data Source=:memory:"));
        });
        appBuilder.Services.AddSingleton<CustomIdValueGenerator>();
        appBuilder
            .Services.AddHttpClient(
                "proscoring-blazor",
                client => client.Timeout = TimeSpan.FromSeconds(ExtendedTimeoutSeconds)
            )
            .AddStandardResilienceHandler();

        // Build and start the application
        var serviceProvider = appBuilder.Services.BuildServiceProvider();
        await using var app = await appBuilder.BuildAsync();
        var resourceNotificationService = app.Services.GetRequiredService<ResourceNotificationService>();
        await app.StartAsync();
        _output.WriteLine("Application started.");

        // Set up the database
        using var scope = serviceProvider.CreateScope();
        var dbContext = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
        await dbContext.Database.EnsureCreatedAsync();

        // Prepare the test data
        var client = app.CreateHttpClient("proscoring-blazor");
        var content = new MultipartFormDataContent
        {
            { new ByteArrayContent([1, 2, 3]), "file", "test.jpg" },
            { new StringContent("Test note"), "note" },
        };

        // Act
        await WaitForResourceAsync(resourceNotificationService, "proscoring-blazor", ExtendedTimeoutSeconds);
        var response = await client.PostAsync("/api/file/upload", content);

        // Assert
        response.EnsureSuccessStatusCode();
        var savedFile = await dbContext.Files.FirstOrDefaultAsync(f =>
            f.TrustedFileNameForDisplay.Contains("test.jpg")
        );
        Assert.NotNull(savedFile);
    }
    #endregion

    #region IDisposable Implementation
    /// <summary>
    /// Performs clean-up of resources used by the test class.
    /// </summary>
    public void Dispose()
    {
        _factory?.Dispose();
        GC.SuppressFinalize(this);
    }
    #endregion
}
