{"workbench.colorCustomizations": {"activityBar.activeBackground": "#c7644b", "activityBar.background": "#c7644b", "activityBar.foreground": "#e7e7e7", "activityBar.inactiveForeground": "#e7e7e799", "activityBarBadge.background": "#6dd281", "activityBarBadge.foreground": "#15202b", "commandCenter.border": "#e7e7e799", "editorGroup.border": "#c7644b", "panel.border": "#c7644b", "sash.hoverBorder": "#c7644b", "sideBar.border": "#c7644b", "statusBar.background": "#aa4d35", "statusBar.debuggingBackground": "#3592aa", "statusBar.debuggingForeground": "#e7e7e7", "statusBar.foreground": "#e7e7e7", "statusBarItem.hoverBackground": "#c7644b", "statusBarItem.remoteBackground": "#aa4d35", "statusBarItem.remoteForeground": "#e7e7e7", "titleBar.activeBackground": "#aa4d35", "titleBar.activeForeground": "#e7e7e7", "titleBar.inactiveBackground": "#aa4d3599", "titleBar.inactiveForeground": "#e7e7e799"}, "peacock.color": "#aa4d35", "cSpell.words": ["AAES", "accessibilities", "Anacortes", "appsettings", "ASPNETCORE", "Blazor", "Blazored", "Bunit", "<PERSON><PERSON>", "goodbyeworld", "Guidish", "HMFIC", "HSTS", "nameof", "Neovolve", "nosnippet", "nowrap", "Npgsql", "onblur", "onclick", "organizingauthorities", "parameterless", "PHRF", "PHRFNW", "proscoring", "<PERSON><PERSON><PERSON>", "rendermode", "rgba", "<PERSON>", "Seaview", "Serilog", "<PERSON><PERSON>", "stylesheet", "S<PERSON><PERSON>", "Westhaven", "<PERSON><PERSON><PERSON>"], "files.associations": {"*.yaml": "yaml", "appsettings*.json": "jsonc"}, "explorer.fileNesting.enabled": true, "explorer.fileNesting.patterns": {"*.razor": "${basename}.razor.css"}}