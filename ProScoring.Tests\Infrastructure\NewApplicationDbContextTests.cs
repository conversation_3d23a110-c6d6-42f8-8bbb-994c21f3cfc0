using System;
using System.Security.Claims;
using System.Threading.Tasks;
using FluentAssertions;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using NSubstitute;
using ProScoring.Domain.Entities;
using ProScoring.Domain.Entities.EntityInterfaces;
using ProScoring.Infrastructure.Database;
using ProScoring.Infrastructure.ServiceInterfaces;
using ProScoring.Infrastructure.Services;
using ProScoring.Tests.Helpers;
using Xunit;

namespace ProScoring.Tests.Infrastructure;

/// <summary>
/// Tests for the new implementation of ApplicationDbContext.
/// These tests focus on change tracking and authentication state handling.
/// </summary>
public sealed class NewApplicationDbContextTests
{
    #region Fields
    private readonly AuthenticationStateProvider _authStateProvider;
    private readonly ApplicationDbContext _context;
    private readonly FixedDateTimeOffsetProvider _dateTimeProvider;
    private readonly IValueGenerator _idGenerator;
    private readonly ILogger<ApplicationDbContext> _logger;
    private readonly string _userId = "U-test-user-id";
    private readonly DateTimeOffset _testTime;
    #endregion

    #region Constructor
    /// <summary>
    /// Initializes a new instance of the <see cref="NewApplicationDbContextTests"/> class.
    /// Sets up the test context with mocked dependencies and a fixed date/time.
    /// </summary>
    public NewApplicationDbContextTests()
    {
        _authStateProvider = Substitute.For<AuthenticationStateProvider>();
        _logger = Substitute.For<ILogger<ApplicationDbContext>>();

        var mockIdService = Substitute.For<IIdGenerationUtilService>();
        mockIdService.GenerateId(Arg.Any<IHasAutoInsertedId>()).Returns(ApplicationDbContext.NULL_USER_ID);

        _idGenerator = new CustomIdValueGenerator(mockIdService, Substitute.For<ILogger<CustomIdValueGenerator>>());
        _dateTimeProvider = new FixedDateTimeOffsetProvider(1776, 7, 4, 12, 0, 0);
        _testTime = _dateTimeProvider.UtcNow;

        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        SetupAuthenticationState();

        _context = new ApplicationDbContext(options, _authStateProvider, _idGenerator, _logger, _dateTimeProvider);
    }
    #endregion

    #region Helper Methods
    /// <summary>
    /// Sets up the authentication state with the specified user ID.
    /// </summary>
    /// <param name="userId">The user ID to set up authentication for. If null, uses the default test user ID.</param>
    private void SetupAuthenticationState(string? userId = null)
    {
        userId ??= _userId;
        var identity = new ClaimsIdentity(new[] { new Claim(ClaimTypes.NameIdentifier, userId) });
        var principal = new ClaimsPrincipal(identity);
        var authState = new AuthenticationState(principal);
        _authStateProvider.GetAuthenticationStateAsync().Returns(Task.FromResult(authState));
    }
    #endregion

    #region methods

    [Fact]
    public void SaveChanges_WhenAddingEntity_SetsChangeTrackingFields()
    {
        // Arrange
        var user = new ApplicationUser();
        _context.Set<ApplicationUser>().Add(user);

        // Act
        _context.SaveChanges();

        // Assert
        user.CreatedAt.Should().Be(_testTime);
        user.UpdatedAt.Should().Be(_testTime);
        user.CreatedById.Should().Be(_userId);
        user.UpdatedById.Should().Be(_userId);
    }

    [Fact]
    public async Task SaveChangesAsync_WhenAddingEntity_SetsChangeTrackingFields()
    {
        // Arrange
        var user = new ApplicationUser();
        await _context.Set<ApplicationUser>().AddAsync(user);

        // Act
        await _context.SaveChangesAsync();

        // Assert
        user.CreatedAt.Should().Be(_testTime);
        user.UpdatedAt.Should().Be(_testTime);
        user.CreatedById.Should().Be(_userId);
        user.UpdatedById.Should().Be(_userId);
    }

    [Fact]
    public async Task SaveChangesAsync_WhenNoChanges_DoesNotUpdateFields()
    {
        // Arrange

        var createTime = _testTime.AddDays(-1);
        _dateTimeProvider.SetDate(createTime.Year, createTime.Month, createTime.Day);
        var user = new ApplicationUser();
        SetupAuthenticationState("original-user");

        await _context.Set<ApplicationUser>().AddAsync(user);
        await _context.SaveChangesAsync();

        _dateTimeProvider.Pop();
        SetupAuthenticationState();

        // Clear tracking to simulate a fresh load
        _context.ChangeTracker.Clear();

        // Reattach without modifications
        _context.Set<ApplicationUser>().Attach(user);

        // Act
        await _context.SaveChangesAsync();

        // Assert
        user.CreatedAt.Should().Be(createTime);
        user.CreatedById.Should().Be("original-user");
        user.UpdatedAt.Should().Be(createTime);
        user.UpdatedById.Should().Be("original-user");
    }

    [Fact]
    public async Task SaveChangesAsync_WhenUpdatingEntity_UpdatesChangeTrackingFields()
    {
        // Arrange
        var createdTime = _testTime.AddDays(-1);
        _dateTimeProvider.SetDate(createdTime.Year, createdTime.Month, createdTime.Day);
        var user = new ApplicationUser();

        SetupAuthenticationState("original-user");
        await _context.Set<ApplicationUser>().AddAsync(user);
        await _context.SaveChangesAsync();
        SetupAuthenticationState();

        _dateTimeProvider.Pop();
        // Clear tracking to simulate a fresh load
        _context.ChangeTracker.Clear();

        // Modify user
        user.PhoneNumber = "modified";
        _context.Set<ApplicationUser>().Update(user);

        // Act
        await _context.SaveChangesAsync();

        // Assert
        user.CreatedAt.Should().Be(createdTime);
        user.CreatedById.Should().Be("original-user");
        user.UpdatedAt.Should().Be(_testTime);
        user.UpdatedById.Should().Be(_userId);
    }

    [Fact]
    public async Task FileRecordSaveChangesAsync_WithNoAuthenticationState_UsesNullUser()
    {
        // Arrange
        _authStateProvider
            .GetAuthenticationStateAsync()
            .Returns(Task.FromException<AuthenticationState>(new InvalidOperationException()));

        var fileRecord = new FileRecord
        {
            ContentType = "application/octet-stream",
            Path = "test/path",
            TrustedFileNameForDisplay = "test.txt",
            UntrustedName = "original.txt",
        };
        await _context.Set<FileRecord>().AddAsync(fileRecord);

        // Act
        await _context.SaveChangesAsync();

        // Assert
        fileRecord.CreatedAt.Should().Be(_testTime);
        fileRecord.UpdatedAt.Should().Be(_testTime);
        fileRecord.CreatedById.Should().Be(ApplicationDbContext.NULL_USER_ID);
        fileRecord.UpdatedById.Should().Be(ApplicationDbContext.NULL_USER_ID);
    }

    [Fact]
    public async Task ApplicationUserSaveChangesAsync_WithNoAuthenticationState_UsesNewUserId()
    {
        // Arrange
        _authStateProvider
            .GetAuthenticationStateAsync()
            .Returns(Task.FromException<AuthenticationState>(new InvalidOperationException()));

        var user = new ApplicationUser
        {
            UserName = "testuser",
            Email = "<EMAIL>",
            Id = "testuser-id",
        };
        await _context.Set<ApplicationUser>().AddAsync(user);

        // Act
        await _context.SaveChangesAsync();

        // Assert
        user.CreatedAt.Should().Be(_testTime);
        user.UpdatedAt.Should().Be(_testTime);
        user.CreatedById.Should().Be(user.Id);
        user.UpdatedById.Should().Be(user.Id);
    }

    #endregion

    #region Test Methods
    /// <summary>
    /// Tests that SaveChanges sets change tracking fields when adding a new entity.
    /// </summary>
    [Fact]
    public void SaveChanges_SetsChangeTrackingFields_WhenAddingEntity()
    {
        // Arrange
        var user = new ApplicationUser();
        _context.Set<ApplicationUser>().Add(user);

        // Act
        _context.SaveChanges();

        // Assert
        user.CreatedAt.Should().Be(_testTime);
        user.UpdatedAt.Should().Be(_testTime);
        user.CreatedById.Should().Be(_userId);
        user.UpdatedById.Should().Be(_userId);
    }

    /// <summary>
    /// Tests that SaveChangesAsync sets change tracking fields when adding a new entity.
    /// </summary>
    [Fact]
    public async Task SaveChangesAsync_SetsChangeTrackingFields_WhenAddingEntity()
    {
        // Arrange
        var user = new ApplicationUser();
        await _context.Set<ApplicationUser>().AddAsync(user);

        // Act
        await _context.SaveChangesAsync();

        // Assert
        user.CreatedAt.Should().Be(_testTime);
        user.UpdatedAt.Should().Be(_testTime);
        user.CreatedById.Should().Be(_userId);
        user.UpdatedById.Should().Be(_userId);
    }

    /// <summary>
    /// Tests that SaveChangesAsync doesn't update tracking fields when there are no changes.
    /// </summary>
    [Fact]
    public async Task SaveChangesAsync_DoesNotUpdateFields_WhenNoChanges()
    {
        // Arrange
        var createTime = _testTime.AddDays(-1);
        _dateTimeProvider.SetDate(createTime.Year, createTime.Month, createTime.Day);
        var user = new ApplicationUser();
        SetupAuthenticationState("original-user");

        await _context.Set<ApplicationUser>().AddAsync(user);
        await _context.SaveChangesAsync();

        _dateTimeProvider.Pop();
        SetupAuthenticationState();

        // Clear tracking to simulate a fresh load
        _context.ChangeTracker.Clear();

        // Reattach without modifications
        _context.Set<ApplicationUser>().Attach(user);

        // Act
        await _context.SaveChangesAsync();

        // Assert
        user.CreatedAt.Should().Be(createTime);
        user.CreatedById.Should().Be("original-user");
        user.UpdatedAt.Should().Be(createTime);
        user.UpdatedById.Should().Be("original-user");
    }

    /// <summary>
    /// Tests that SaveChangesAsync updates tracking fields when updating an entity.
    /// </summary>
    [Fact]
    public async Task SaveChangesAsync_UpdatesTrackingFields_WhenUpdatingEntity()
    {
        // Arrange
        var createdTime = _testTime.AddDays(-1);
        _dateTimeProvider.SetDate(createdTime.Year, createdTime.Month, createdTime.Day);
        var user = new ApplicationUser();
        SetupAuthenticationState("original-user");

        await _context.Set<ApplicationUser>().AddAsync(user);
        await _context.SaveChangesAsync();

        _dateTimeProvider.Pop();
        SetupAuthenticationState();

        // Clear tracking to simulate a fresh load
        _context.ChangeTracker.Clear();

        // Reattach and modify
        var attachedUser = _context.Set<ApplicationUser>().Attach(user);
        attachedUser.Entity.UserName = "testuser";
        attachedUser.State = EntityState.Modified;

        // Act
        await _context.SaveChangesAsync();

        // Assert
        user.CreatedAt.Should().Be(createdTime);
        user.CreatedById.Should().Be("original-user");
        user.UpdatedAt.Should().Be(_testTime);
        user.UpdatedById.Should().Be(_userId);
    }

    /// <summary>
    /// Tests that FileRecord save changes handle null authentication state correctly.
    /// </summary>
    [Fact]
    public async Task FileRecordSaveChangesAsync_UsesNullUser_WhenNoAuthenticationState()
    {
        // Arrange
        _authStateProvider
            .GetAuthenticationStateAsync()
            .Returns(Task.FromResult(new AuthenticationState(new ClaimsPrincipal())));

        var file = new FileRecord
        {
            ContentType = "text/plain",
            Path = "test/path",
            TrustedFileNameForDisplay = "test.txt",
            UntrustedName = "test.txt",
        };

        // Act
        await _context.Set<FileRecord>().AddAsync(file);
        await _context.SaveChangesAsync();

        // Assert
        file.CreatedById.Should().Be(ApplicationDbContext.NULL_USER_ID);
        file.UpdatedById.Should().Be(ApplicationDbContext.NULL_USER_ID);
        file.CreatedAt.Should().Be(_testTime);
        file.UpdatedAt.Should().Be(_testTime);
    }

    /// <summary>
    /// Tests that ApplicationUser save changes generate a new user ID when there's no authentication state.
    /// </summary>
    [Fact]
    public async Task ApplicationUserSaveChangesAsync_GeneratesNewUserId_WithNoAuthenticationState()
    {
        // Arrange
        _authStateProvider
            .GetAuthenticationStateAsync()
            .Returns(Task.FromResult(new AuthenticationState(new ClaimsPrincipal())));

        var user = new ApplicationUser();

        // Act
        await _context.Set<ApplicationUser>().AddAsync(user);
        await _context.SaveChangesAsync();

        // Assert
        user.CreatedById.Should().Be(ApplicationDbContext.NULL_USER_ID);
        user.UpdatedById.Should().Be(user.CreatedById);
        user.Id.Should().Be(user.CreatedById);
        user.CreatedAt.Should().Be(_testTime);
        user.UpdatedAt.Should().Be(_testTime);
    }
    #endregion
}
