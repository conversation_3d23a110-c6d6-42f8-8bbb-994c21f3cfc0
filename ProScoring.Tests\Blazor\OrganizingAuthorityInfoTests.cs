using AutoFixture;
using Bunit;
// using Moq; // Removed as per instructions
using Bunit.TestDoubles; // Added for FakeNavigationManager
using FluentAssertions;
using Microsoft.AspNetCore.Components; // Added for NavigationManager
using Microsoft.Extensions.DependencyInjection; // Added for service registration
using ProScoring.Blazor.Components.Shared;
using ProScoring.Domain.Dtos;

namespace ProScoring.Tests.Blazor;

/// <summary>
/// Tests for the <see cref="OrganizingAuthorityInfo"/> component.
/// These tests verify that the component correctly displays organizing authority information
/// in different layouts and with varying amounts of data.
/// </summary>
public class OrganizingAuthorityInfoTests : TestContext
{
    #region Fields

    private readonly Fixture _fixture;
    // private readonly Mock<NavigationManager> _mockNavigationManager; // Removed

    #endregion Fields

    #region Constructor

    public OrganizingAuthorityInfoTests()
    {
        _fixture = new Fixture();
        JSInterop.Mode = JSRuntimeMode.Loose;

        // Removed Moq NavigationManager registration
        // Services.AddSingleton(_mockNavigationManager.Object);
        // bUnit automatically registers FakeNavigationManager
    }

    #endregion Constructor

    #region Test Methods - Card Layout

    /// <summary>
    /// Verifies that when using card layout with complete authority data,
    /// all information is properly displayed.
    /// </summary>
    [Fact]
    public void CardLayout_WithFullData_DisplaysAllInformation()
    {
        // Arrange
        var authority = CreateFullAuthorityDto();

        // Act
        var cut = RenderComponent<OrganizingAuthorityInfo>(parameters =>
            parameters
                .Add(p => p.Authority, authority)
                .Add(p => p.DisplayMode, DisplayMode.Card)
                .Add(p => p.ShowAdditionalInfo, true)
        );

        // Assert
        cut.Find("[data-testid='oa-info-card']").Should().NotBeNull();
        cut.Find("[data-testid='oa-burgee-image']").Should().NotBeNull();
        cut.Find("[data-testid='oa-name']").TextContent.Should().Contain(authority.Name);
        cut.Find("[data-testid='oa-state']").TextContent.Should().Contain(authority.State!);
        cut.Find("[data-testid='oa-country']").TextContent.Should().Contain(authority.Country!);
        cut.Find("[data-testid='oa-city']").TextContent.Should().Contain(authority.City!);
        cut.Find("[data-testid='oa-email']").TextContent.Should().Contain(authority.Email!);
        cut.Find("[data-testid='oa-phone']").TextContent.Should().Contain(authority.Phone!);
        cut.Find("a[data-testid='oa-website-link']").Should().NotBeNull(); // Changed to be more specific for anchor
    }

    /// <summary>
    /// Verifies that when using card layout with minimal authority data,
    /// only the available information is displayed.
    /// </summary>
    [Fact]
    public void CardLayout_WithMinimalData_DisplaysOnlyAvailableInformation()
    {
        // Arrange
        var authority = CreateMinimalAuthorityDto();

        // Act
        var cut = RenderComponent<OrganizingAuthorityInfo>(parameters =>
            parameters
                .Add(p => p.Authority, authority)
                .Add(p => p.DisplayMode, DisplayMode.Card)
                .Add(p => p.ShowAdditionalInfo, true)
        );

        // Assert
        cut.Find("[data-testid='oa-info-card']").Should().NotBeNull();
        cut.Find("[data-testid='oa-name']").TextContent.Should().Contain(authority.Name);
        cut.FindAll("[data-testid='oa-state']").Count.Should().Be(0);
        cut.FindAll("[data-testid='oa-country']").Count.Should().Be(0);
        cut.FindAll("[data-testid='oa-city']").Count.Should().Be(0);
        cut.FindAll("[data-testid='oa-email']").Count.Should().Be(0);
        cut.FindAll("[data-testid='oa-phone']").Count.Should().Be(0);
        cut.FindAll("a[data-testid='oa-website-link']").Count.Should().Be(0); // Changed for anchor
    }

    /// <summary>
    /// Verifies that when using card layout without additional info flag,
    /// extra details are hidden while maintaining core information.
    /// </summary>
    [Fact]
    public void CardLayout_WithoutAdditionalInfo_HidesExtraDetails()
    {
        // Arrange
        var authority = CreateFullAuthorityDto();

        // Act
        var cut = RenderComponent<OrganizingAuthorityInfo>(parameters =>
            parameters
                .Add(p => p.Authority, authority)
                .Add(p => p.DisplayMode, DisplayMode.Card)
                .Add(p => p.ShowAdditionalInfo, false)
        );

        // Assert
        cut.Find("[data-testid='oa-info-card']").Should().NotBeNull();
        cut.Find("[data-testid='oa-name']").TextContent.Should().Contain(authority.Name);
        cut.Find("[data-testid='oa-state']").TextContent.Should().Contain(authority.State!);
        cut.Find("[data-testid='oa-country']").TextContent.Should().Contain(authority.Country!);
        cut.Find("[data-testid='oa-city']").TextContent.Should().Contain(authority.City!);
        cut.FindAll("[data-testid='oa-email']").Count.Should().Be(0);
        cut.FindAll("[data-testid='oa-phone']").Count.Should().Be(0);
        cut.FindAll("a[data-testid='oa-website-link']").Count.Should().Be(0); // Changed for anchor
    }

    #endregion Test Methods - Card Layout

    #region Test Methods - Row Layout

    /// <summary>
    /// Verifies that when using row layout with complete authority data,
    /// all information is properly displayed.
    /// </summary>
    [Fact]
    public void RowLayout_WithFullData_DisplaysAllInformation()
    {
        // Arrange
        var authority = CreateFullAuthorityDto();

        // Act
        var cut = RenderComponent<OrganizingAuthorityInfo>(parameters =>
            parameters
                .Add(p => p.Authority, authority)
                .Add(p => p.DisplayMode, DisplayMode.Row)
                .Add(p => p.ShowAdditionalInfo, true)
        );

        // Assert
        cut.Find("[data-testid='oa-info-row']").Should().NotBeNull();
        cut.Find("[data-testid='oa-burgee-image-small']").Should().NotBeNull();
        cut.Find("[data-testid='oa-name']").TextContent.Should().Contain(authority.Name);
        cut.Find("[data-testid='oa-state']").TextContent.Should().Contain(authority.State!);
        cut.Find("[data-testid='oa-country']").TextContent.Should().Contain(authority.Country!);
        cut.Find("[data-testid='oa-city']").TextContent.Should().Contain(authority.City!);
        cut.Find("[data-testid='oa-email']").TextContent.Should().Contain(authority.Email!);
        cut.Find("[data-testid='oa-phone']").TextContent.Should().Contain(authority.Phone!);
        cut.Find("a[data-testid='oa-website-link']").Should().NotBeNull(); // Changed for anchor
    }

    /// <summary>
    /// Verifies that when using row layout with minimal authority data,
    /// only the available information is displayed.
    /// </summary>
    [Fact]
    public void RowLayout_WithMinimalData_DisplaysOnlyAvailableInformation()
    {
        // Arrange
        var authority = CreateMinimalAuthorityDto();

        // Act
        var cut = RenderComponent<OrganizingAuthorityInfo>(parameters =>
            parameters
                .Add(p => p.Authority, authority)
                .Add(p => p.DisplayMode, DisplayMode.Row)
                .Add(p => p.ShowAdditionalInfo, true)
        );

        // Assert
        cut.Find("[data-testid='oa-info-row']").Should().NotBeNull();
        cut.Find("[data-testid='oa-name']").TextContent.Should().Contain(authority.Name);
        cut.FindAll("[data-testid='oa-state']").Count.Should().Be(0);
        cut.FindAll("[data-testid='oa-country']").Count.Should().Be(0);
        cut.FindAll("[data-testid='oa-city']").Count.Should().Be(0);
        cut.FindAll("[data-testid='oa-email']").Count.Should().Be(0);
        cut.FindAll("[data-testid='oa-phone']").Count.Should().Be(0);
        cut.FindAll("a[data-testid='oa-website-link']").Count.Should().Be(0); // Changed for anchor
    }

    /// <summary>
    /// Verifies that when using row layout without additional info flag,
    /// extra details are hidden while maintaining core information.
    /// </summary>
    [Fact]
    public void RowLayout_WithoutAdditionalInfo_HidesExtraDetails()
    {
        // Arrange
        var authority = CreateFullAuthorityDto();

        // Act
        var cut = RenderComponent<OrganizingAuthorityInfo>(parameters =>
            parameters
                .Add(p => p.Authority, authority)
                .Add(p => p.DisplayMode, DisplayMode.Row)
                .Add(p => p.ShowAdditionalInfo, false)
        );

        // Assert
        cut.Find("[data-testid='oa-info-row']").Should().NotBeNull();
        cut.Find("[data-testid='oa-name']").TextContent.Should().Contain(authority.Name);
        cut.Find("[data-testid='oa-state']").TextContent.Should().Contain(authority.State!);
        cut.Find("[data-testid='oa-country']").TextContent.Should().Contain(authority.Country!);
        cut.Find("[data-testid='oa-city']").TextContent.Should().Contain(authority.City!);
        cut.FindAll("[data-testid='oa-email']").Count.Should().Be(0);
        cut.FindAll("[data-testid='oa-phone']").Count.Should().Be(0);
        cut.FindAll("a[data-testid='oa-website-link']").Count.Should().Be(0); // Changed for anchor
    }

    #endregion Test Methods - Row Layout

    #region Test Methods - Edge Cases

    /// <summary>
    /// Verifies that when the authority is null, a placeholder is displayed.
    /// </summary>
    [Fact]
    public void NullAuthority_DisplaysPlaceholder()
    {
        // Arrange & Act
        var cut = RenderComponent<OrganizingAuthorityInfo>(parameters =>
            parameters.Add(p => p.Authority, null).Add(p => p.DisplayMode, DisplayMode.Card)
        );

        // Assert
        cut.Find("[data-testid='oa-info-placeholder']").Should().NotBeNull();
        cut.Markup.Should().Contain("No organizing authority information available");
    }

    /// <summary>
    /// Verifies that custom styles are correctly applied to both card and row layouts.
    /// </summary>
    [Fact]
    public void CustomStyles_AreAppliedCorrectly()
    {
        // Arrange
        var authority = CreateMinimalAuthorityDto();
        var customCardStyle = "background-color: red; width: 300px;";

        // Act - Card Mode
        var cardCut = RenderComponent<OrganizingAuthorityInfo>(parameters =>
            parameters
                .Add(p => p.Authority, authority)
                .Add(p => p.DisplayMode, DisplayMode.Card)
                .Add(p => p.CardStyle, customCardStyle)
        );

        // Assert
        cardCut.Find("[data-testid='oa-info-card']").GetAttribute("style").Should().Contain(customCardStyle);
    }

    #endregion Methods

    #region Navigation Tests

    [Fact]
    public void CardMode_WhenEnableNavigationOnClickIsTrue_NavigatesToDetailsPageOnClick()
    {
        // Arrange
        var authority = CreateFullAuthorityDto();
        var expectedUri = $"/organizingauthorities/details?id={authority.Id}";

        var cut = RenderComponent<OrganizingAuthorityInfo>(parameters =>
            parameters
                .Add(p => p.Authority, authority)
                .Add(p => p.DisplayMode, DisplayMode.Card)
                .Add(p => p.EnableNavigationOnClick, true)
        );

        // Act
        cut.Find("[data-testid='oa-info-card']").Click();

        // Assert
        var navManager = Services.GetRequiredService<FakeNavigationManager>();
        navManager.Uri.Should().Be($"http://localhost/{expectedUri.TrimStart('/')}");
    }

    [Fact]
    public void CardMode_WhenEnableNavigationOnClickIsFalse_DoesNotNavigateOnClick()
    {
        // Arrange
        var authority = CreateFullAuthorityDto();

        var cut = RenderComponent<OrganizingAuthorityInfo>(parameters =>
            parameters
                .Add(p => p.Authority, authority)
                .Add(p => p.DisplayMode, DisplayMode.Card)
                .Add(p => p.EnableNavigationOnClick, false)
        );

        // Act
        cut.Find("[data-testid='oa-info-card']").Click();

        // Assert
        var navManager = Services.GetRequiredService<FakeNavigationManager>();
        navManager.History.Count.Should().Be(0);
    }

    [Fact]
    public void RowMode_WhenEnableNavigationOnClickIsTrue_NavigatesToDetailsPageOnClick()
    {
        // Arrange
        var authority = CreateFullAuthorityDto();
        var expectedUri = $"/organizingauthorities/details?id={authority.Id}";

        var cut = RenderComponent<OrganizingAuthorityInfo>(parameters =>
            parameters
                .Add(p => p.Authority, authority)
                .Add(p => p.DisplayMode, DisplayMode.Row)
                .Add(p => p.EnableNavigationOnClick, true)
        );

        // Act
        cut.Find("[data-testid='oa-info-row']").Click();

        // Assert
        var navManager = Services.GetRequiredService<FakeNavigationManager>();
        navManager.Uri.Should().Be($"http://localhost/{expectedUri.TrimStart('/')}");
    }

    [Fact]
    public void RowMode_WhenEnableNavigationOnClickIsFalse_DoesNotNavigateOnClick()
    {
        // Arrange
        var authority = CreateFullAuthorityDto();

        var cut = RenderComponent<OrganizingAuthorityInfo>(parameters =>
            parameters
                .Add(p => p.Authority, authority)
                .Add(p => p.DisplayMode, DisplayMode.Row)
                .Add(p => p.EnableNavigationOnClick, false)
        );

        // Act
        cut.Find("[data-testid='oa-info-row']").Click();

        // Assert
        var navManager = Services.GetRequiredService<FakeNavigationManager>();
        navManager.History.Count.Should().Be(0);
    }

    [Fact]
    public void Click_WhenAuthorityIsNullAndNavigationEnabled_DoesNotNavigate()
    {
        // Arrange
        // No explicit cut.Find().Click() is needed if the clickable element isn't rendered.
        // The component is rendered, and we check that no navigation happened by default.
        RenderComponent<OrganizingAuthorityInfo>(parameters =>
            parameters
                .Add(p => p.Authority, null)
                .Add(p => p.DisplayMode, DisplayMode.Card)
                .Add(p => p.EnableNavigationOnClick, true)
        );

        // Assert
        var navManager = Services.GetRequiredService<FakeNavigationManager>();
        navManager.History.Count.Should().Be(0);
    }

    [Fact]
    public void Click_WhenAuthorityIdIsNullAndNavigationEnabled_DoesNotNavigate()
    {
        // Arrange
        var authority = CreateMinimalAuthorityDto();
        authority.Id = null;

        var cut = RenderComponent<OrganizingAuthorityInfo>(parameters =>
            parameters
                .Add(p => p.Authority, authority)
                .Add(p => p.DisplayMode, DisplayMode.Card)
                .Add(p => p.EnableNavigationOnClick, true)
        );

        // Act
        cut.Find("[data-testid='oa-info-card']").Click();

        // Assert
        var navManager = Services.GetRequiredService<FakeNavigationManager>();
        navManager.History.Count.Should().Be(0);
    }

    #endregion Navigation Tests

    #region Contact Detail Tests

    [Fact]
    public void CardMode_WithShowAdditionalInfoTrue_AndParentNavigationEnabled_DisplaysWebsiteLinkCorrectly()
    {
        // Arrange
        var authority = CreateFullAuthorityDto();
        authority.Email = null;
        authority.Phone = null;

        var cut = RenderComponent<OrganizingAuthorityInfo>(parameters =>
            parameters
                .Add(p => p.Authority, authority)
                .Add(p => p.DisplayMode, DisplayMode.Card)
                .Add(p => p.ShowAdditionalInfo, true)
                .Add(p => p.EnableNavigationOnClick, true)
        );

        // Assert
        var websiteLink = cut.FindAll("a[data-testid='oa-website-link']");
        websiteLink.Should().HaveCount(1);
        websiteLink.First().GetAttribute("href").Should().Be(authority.Website);
        websiteLink.First().TextContent.Should().Be(authority.Website);
        websiteLink.First().GetAttribute("target").Should().Be("_blank");
    }

    [Fact]
    public void RowMode_WithShowAdditionalInfoTrue_AndParentNavigationEnabled_DisplaysWebsiteLinkCorrectly()
    {
        // Arrange
        var authority = CreateFullAuthorityDto();
        authority.Email = null;
        authority.Phone = null;

        var cut = RenderComponent<OrganizingAuthorityInfo>(parameters =>
            parameters
                .Add(p => p.Authority, authority)
                .Add(p => p.DisplayMode, DisplayMode.Row)
                .Add(p => p.ShowAdditionalInfo, true)
                .Add(p => p.EnableNavigationOnClick, true)
        );

        // Assert
        var websiteLink = cut.FindAll("a[data-testid='oa-website-link']");
        websiteLink.Should().HaveCount(1);
        websiteLink.First().GetAttribute("href").Should().Be(authority.Website);
        websiteLink.First().TextContent.Should().Be(authority.Website);
        websiteLink.First().GetAttribute("target").Should().Be("_blank");
    }

    [Fact]
    public void CardMode_WithShowAdditionalInfoFalse_DoesNotDisplayWebsiteLink()
    {
        // Arrange
        var authority = CreateFullAuthorityDto();

        var cut = RenderComponent<OrganizingAuthorityInfo>(parameters =>
            parameters
                .Add(p => p.Authority, authority)
                .Add(p => p.DisplayMode, DisplayMode.Card)
                .Add(p => p.ShowAdditionalInfo, false)
                .Add(p => p.EnableNavigationOnClick, true)
        );

        // Assert
        cut.FindAll("a[data-testid='oa-website-link']").Should().BeEmpty();
    }

    [Fact]
    public void RowMode_WithNullWebsite_DoesNotDisplayWebsiteLink()
    {
        // Arrange
        var authority = CreateFullAuthorityDto();
        authority.Website = null;

        var cut = RenderComponent<OrganizingAuthorityInfo>(parameters =>
            parameters
                .Add(p => p.Authority, authority)
                .Add(p => p.DisplayMode, DisplayMode.Row)
                .Add(p => p.ShowAdditionalInfo, true)
                .Add(p => p.EnableNavigationOnClick, true)
        );

        // Assert
        cut.FindAll("a[data-testid='oa-website-link']").Should().BeEmpty();
    }

    [Fact]
    public void CardMode_WithFullData_EmailAndPhoneAreNotRenderedAsLinks()
    {
        // Arrange
        var authority = CreateFullAuthorityDto();

        var cut = RenderComponent<OrganizingAuthorityInfo>(parameters =>
            parameters
                .Add(p => p.Authority, authority)
                .Add(p => p.DisplayMode, DisplayMode.Card)
                .Add(p => p.ShowAdditionalInfo, true)
        );

        // Assert
        cut.Find("[data-testid='oa-email']").TextContent.Should().Contain(authority.Email!);
        cut.FindAll("a[href^='mailto:']").Should().BeEmpty();

        cut.Find("[data-testid='oa-phone']").TextContent.Should().Contain(authority.Phone!);
        cut.FindAll("a[href^='tel:']").Should().BeEmpty();
    }

    [Fact]
    public void RowMode_WithFullData_EmailAndPhoneAreNotRenderedAsLinks()
    {
        // Arrange
        var authority = CreateFullAuthorityDto();

        var cut = RenderComponent<OrganizingAuthorityInfo>(parameters =>
            parameters
                .Add(p => p.Authority, authority)
                .Add(p => p.DisplayMode, DisplayMode.Row)
                .Add(p => p.ShowAdditionalInfo, true)
        );

        // Assert
        cut.Find("[data-testid='oa-email']").TextContent.Should().Contain(authority.Email!);
        cut.FindAll("a[href^='mailto:']").Should().BeEmpty();

        cut.Find("[data-testid='oa-phone']").TextContent.Should().Contain(authority.Phone!);
        cut.FindAll("a[href^='tel:']").Should().BeEmpty();
    }

    #endregion Contact Detail Tests

    private OrganizingAuthorityInfoDto CreateFullAuthorityDto()
    {
        return new OrganizingAuthorityInfoDto
        {
            Id = "O123",
            Name = "Seattle Yacht Club",
            Email = "<EMAIL>",
            Phone = "************",
            Website = "https://seattleyachtclub.org",
            City = "Seattle",
            State = "Washington",
            Country = "USA",
            ImageId = "img123",
            Private = false,
        };
    }

    private OrganizingAuthorityInfoDto CreateMinimalAuthorityDto()
    {
        return new OrganizingAuthorityInfoDto
        {
            Id = "O456",
            Name = "Minimal Yacht Club",
            ImageId = null,
            Private = true,
            // Website, Email, Phone, City, State, Country are null by default
        };
    }
}
